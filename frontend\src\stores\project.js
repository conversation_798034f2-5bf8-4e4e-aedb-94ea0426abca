import { defineStore } from 'pinia'
import { ref } from 'vue'
import axios from 'axios'

const API_BASE = 'http://localhost:8000/api'

export const useProjectStore = defineStore('project', () => {
  // State
  const currentProject = ref(null)
  const projects = ref([])
  const loading = ref(false)

  // Actions
  const createProject = async (projectData) => {
    try {
      loading.value = true
      
      const response = await axios.post(`${API_BASE}/project/create`, projectData)
      
      currentProject.value = {
        id: response.data.project_id,
        ...projectData
      }
      
      return response.data
    } catch (error) {
      console.error('Failed to create project:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const saveProject = async (projectId, projectData) => {
    try {
      loading.value = true
      
      const response = await axios.put(`${API_BASE}/project/save/${projectId}`, projectData)
      
      if (currentProject.value && currentProject.value.id === projectId) {
        Object.assign(currentProject.value, projectData)
      }
      
      return response.data
    } catch (error) {
      console.error('Failed to save project:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const loadProject = async (projectId) => {
    try {
      loading.value = true
      
      const response = await axios.get(`${API_BASE}/project/load/${projectId}`)
      const projectData = response.data
      
      currentProject.value = projectData
      
      return projectData
    } catch (error) {
      console.error('Failed to load project:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getProjectList = async () => {
    try {
      loading.value = true
      
      const response = await axios.get(`${API_BASE}/project/list`)
      projects.value = response.data.projects
      
      return projects.value
    } catch (error) {
      console.error('Failed to get project list:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteProject = async (projectId) => {
    try {
      loading.value = true
      
      await axios.delete(`${API_BASE}/project/delete/${projectId}`)
      
      // Remove from local list
      const index = projects.value.findIndex(p => p.id === projectId)
      if (index !== -1) {
        projects.value.splice(index, 1)
      }
      
      // Clear current project if it's the deleted one
      if (currentProject.value && currentProject.value.id === projectId) {
        currentProject.value = null
      }
      
    } catch (error) {
      console.error('Failed to delete project:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const exportVideo = async (projectData, outputSettings = {}) => {
    try {
      loading.value = true
      
      const response = await axios.post(`${API_BASE}/export/start`, {
        project_data: projectData,
        output_settings: outputSettings
      })
      
      return response.data
    } catch (error) {
      console.error('Failed to start export:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getExportStatus = async (exportId) => {
    try {
      const response = await axios.get(`${API_BASE}/export/status/${exportId}`)
      return response.data
    } catch (error) {
      console.error('Failed to get export status:', error)
      throw error
    }
  }

  const downloadExport = (exportId) => {
    const downloadUrl = `${API_BASE}/export/download/${exportId}`
    window.open(downloadUrl, '_blank')
  }

  return {
    // State
    currentProject,
    projects,
    loading,
    
    // Actions
    createProject,
    saveProject,
    loadProject,
    getProjectList,
    deleteProject,
    exportVideo,
    getExportStatus,
    downloadExport
  }
})
