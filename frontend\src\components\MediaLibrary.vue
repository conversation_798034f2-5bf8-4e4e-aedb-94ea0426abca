<template>
  <div class="media-library">
    <div class="library-header">
      <h3>素材库</h3>
      <NButton @click="uploadVideo" type="primary" size="small">
        上传视频
      </NButton>
    </div>
    
    <div class="library-content">
      <div v-if="loading" class="loading">
        <NSpin size="small" />
        <span>加载中...</span>
      </div>
      
      <div v-else-if="videos.length === 0" class="empty">
        <NEmpty description="暂无视频文件" />
      </div>
      
      <div v-else class="video-list">
        <div 
          v-for="video in videos"
          :key="video.id"
          class="video-item"
          draggable="true"
          @dragstart="onDragStart($event, video)"
        >
          <div class="video-thumbnail">
            <img 
              v-if="video.thumbnail" 
              :src="`http://localhost:8000/${video.thumbnail}`"
              :alt="video.filename"
              @error="onImageError"
            />
            <div v-else class="thumbnail-placeholder">
              <span>视频</span>
            </div>
          </div>
          
          <div class="video-info">
            <div class="video-name" :title="video.filename">
              {{ video.filename }}
            </div>
            <div class="video-details">
              <span>{{ formatDuration(video.duration) }}</span>
              <span>{{ video.width }}x{{ video.height }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Hidden file input -->
    <input 
      ref="fileInput"
      type="file"
      accept="video/*"
      multiple
      style="display: none"
      @change="onFileSelect"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { NButton, NEmpty, NSpin } from 'naive-ui'
import { useTimelineStore } from '../stores/timeline'
import { useMediaStore } from '../stores/media'

const timelineStore = useTimelineStore()
const mediaStore = useMediaStore()

const fileInput = ref(null)
const loading = ref(false)
const videos = ref([])

const uploadVideo = () => {
  fileInput.value?.click()
}

const onFileSelect = async (event) => {
  const files = Array.from(event.target.files)
  if (files.length === 0) return
  
  loading.value = true
  
  try {
    for (const file of files) {
      await mediaStore.uploadVideo(file)
    }
    await loadVideos()
  } catch (error) {
    console.error('Upload failed:', error)
  } finally {
    loading.value = false
    event.target.value = '' // Reset input
  }
}

const loadVideos = async () => {
  try {
    loading.value = true
    videos.value = await mediaStore.getVideoList()
  } catch (error) {
    console.error('Failed to load videos:', error)
  } finally {
    loading.value = false
  }
}

const onDragStart = (event, video) => {
  event.dataTransfer.setData('application/json', JSON.stringify({
    type: 'video',
    video: video
  }))
}

const onImageError = (event) => {
  event.target.style.display = 'none'
}

const formatDuration = (duration) => {
  const minutes = Math.floor(duration / 60)
  const seconds = Math.floor(duration % 60)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

onMounted(() => {
  loadVideos()
})
</script>

<style scoped>
.media-library {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.library-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.library-header h3 {
  margin: 0;
  font-size: 16px;
}

.library-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #666;
}

.empty {
  padding: 20px;
}

.video-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.video-item {
  display: flex;
  gap: 8px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: grab;
  transition: all 0.2s;
}

.video-item:hover {
  border-color: #18a058;
  background: #f0f9ff;
}

.video-item:active {
  cursor: grabbing;
}

.video-thumbnail {
  width: 60px;
  height: 34px;
  border-radius: 2px;
  overflow: hidden;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  font-size: 10px;
  color: #999;
}

.video-info {
  flex: 1;
  min-width: 0;
}

.video-name {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.video-details {
  font-size: 10px;
  color: #666;
  display: flex;
  gap: 8px;
}
</style>
