from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse, FileResponse
import os
import uuid
import shutil
from typing import List

from app.core.video_processor import VideoProcessor
from app.core.thumbnail_generator import ThumbnailGenerator

router = APIRouter()
video_processor = VideoProcessor()
thumbnail_generator = ThumbnailGenerator()

@router.post("/upload")
async def upload_video(file: UploadFile = File(...)):
    """Upload a video file"""
    try:
        # Generate unique filename
        file_id = str(uuid.uuid4())
        file_extension = os.path.splitext(file.filename)[1]
        filename = f"{file_id}{file_extension}"
        file_path = os.path.join("uploads", filename)
        
        # Save file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Get video info
        video_info = video_processor.get_video_info(file_path)
        
        # Generate thumbnail
        thumbnail_path = await thumbnail_generator.generate_thumbnail(file_path, file_id)
        
        return {
            "id": file_id,
            "filename": file.filename,
            "path": file_path,
            "thumbnail": thumbnail_path,
            "duration": video_info["duration"],
            "width": video_info["width"],
            "height": video_info["height"],
            "fps": video_info["fps"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/info/{video_id}")
async def get_video_info(video_id: str):
    """Get video information"""
    try:
        file_path = f"uploads/{video_id}"
        # Find the actual file with extension
        for ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv']:
            if os.path.exists(f"{file_path}{ext}"):
                file_path = f"{file_path}{ext}"
                break
        
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="Video not found")
        
        video_info = video_processor.get_video_info(file_path)
        return video_info
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/list")
async def list_videos():
    """List all uploaded videos"""
    try:
        videos = []
        uploads_dir = "uploads"
        if os.path.exists(uploads_dir):
            for filename in os.listdir(uploads_dir):
                if filename.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv')):
                    file_path = os.path.join(uploads_dir, filename)
                    file_id = os.path.splitext(filename)[0]
                    
                    try:
                        video_info = video_processor.get_video_info(file_path)
                        videos.append({
                            "id": file_id,
                            "filename": filename,
                            "path": file_path,
                            "thumbnail": f"thumbnails/{file_id}.jpg",
                            "duration": video_info["duration"],
                            "width": video_info["width"],
                            "height": video_info["height"],
                            "fps": video_info["fps"]
                        })
                    except:
                        continue
        
        return {"videos": videos}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/file/{video_id}")
async def get_video_file(video_id: str):
    """Get video file by ID (with automatic extension detection)"""
    try:
        # Find the actual file with extension
        for ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.webm', '.flv']:
            file_path = os.path.join("uploads", f"{video_id}{ext}")
            if os.path.exists(file_path):
                return FileResponse(
                    file_path,
                    media_type=f"video/{ext[1:]}",
                    filename=f"{video_id}{ext}"
                )

        raise HTTPException(status_code=404, detail="Video file not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
