import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useTimelineStore = defineStore('timeline', () => {
  // State
  const tracks = ref([])
  const currentTime = ref(0)
  const duration = ref(0)
  const zoom = ref(1)
  const isPlaying = ref(false)
  const selectedSegment = ref(null)
  const currentPreviewVideo = ref(null)
  const currentCropParams = ref(null)

  // Getters
  const fps = computed(() => 30) // Fixed FPS for now
  const frameTime = computed(() => 1 / fps.value)
  
  const currentFrame = computed(() => {
    return Math.round(currentTime.value * fps.value)
  })

  // Actions
  const setCurrentTime = (time) => {
    currentTime.value = Math.max(0, Math.min(time, duration.value))
    updatePreviewVideo()
  }

  const setDuration = (newDuration) => {
    duration.value = newDuration
  }

  const setZoom = (newZoom) => {
    zoom.value = Math.max(0.1, Math.min(10, newZoom))
  }

  const togglePlayback = () => {
    isPlaying.value = !isPlaying.value
  }

  const addTrack = () => {
    const trackId = `track_${Date.now()}`
    tracks.value.push({
      id: trackId,
      name: `轨道 ${tracks.value.length + 1}`,
      type: 'video',
      segments: [],
      enabled: true
    })
  }

  const removeTrack = (trackId) => {
    const index = tracks.value.findIndex(track => track.id === trackId)
    if (index !== -1) {
      tracks.value.splice(index, 1)
    }
  }

  const addSegmentToTrack = (trackId, segment) => {
    const track = tracks.value.find(t => t.id === trackId)
    if (track) {
      const segmentId = `segment_${Date.now()}`
      const newSegment = {
        id: segmentId,
        sourceVideoId: segment.sourceVideoId,
        startTime: segment.startTime || 0,
        endTime: segment.endTime || segment.duration || 10,
        startLoc: segment.startLoc || 0,
        endLoc: segment.endLoc || (segment.duration || 10),
        cropParams: segment.cropParams || null,
        ...segment
      }
      track.segments.push(newSegment)
      
      // Update timeline duration if needed
      const segmentEnd = newSegment.endLoc
      if (segmentEnd > duration.value) {
        setDuration(segmentEnd)
      }
      
      return newSegment
    }
    return null
  }

  const removeSegment = (segmentId) => {
    for (const track of tracks.value) {
      const index = track.segments.findIndex(s => s.id === segmentId)
      if (index !== -1) {
        track.segments.splice(index, 1)
        break
      }
    }
    
    if (selectedSegment.value?.id === segmentId) {
      selectedSegment.value = null
      currentPreviewVideo.value = null
      currentCropParams.value = null
    }
  }

  const updateSegment = (segmentId, updates) => {
    for (const track of tracks.value) {
      const segment = track.segments.find(s => s.id === segmentId)
      if (segment) {
        Object.assign(segment, updates)
        
        // Update timeline duration if needed
        if (updates.endLoc && updates.endLoc > duration.value) {
          setDuration(updates.endLoc)
        }
        
        // Update preview if this is the selected segment
        if (selectedSegment.value?.id === segmentId) {
          selectedSegment.value = segment
          updatePreviewVideo()
        }
        break
      }
    }
  }

  const selectSegment = (segment) => {
    selectedSegment.value = segment
    currentCropParams.value = segment?.cropParams || null
    updatePreviewVideo()
  }

  const updateCropParams = (params) => {
    currentCropParams.value = params
    if (selectedSegment.value) {
      updateSegment(selectedSegment.value.id, { cropParams: params })
    }
  }

  const updatePreviewVideo = () => {
    if (!selectedSegment.value) {
      currentPreviewVideo.value = null
      return
    }

    // Find the video file for the selected segment
    const segment = selectedSegment.value
    const videoUrl = `http://localhost:8000/uploads/${segment.sourceVideoId}`
    
    currentPreviewVideo.value = {
      url: videoUrl,
      segment: segment,
      currentTime: currentTime.value
    }
  }

  const getSegmentAtTime = (time) => {
    for (const track of tracks.value) {
      for (const segment of track.segments) {
        if (time >= segment.startLoc && time <= segment.endLoc) {
          return segment
        }
      }
    }
    return null
  }

  const splitSegment = (segmentId, splitTime) => {
    for (const track of tracks.value) {
      const segmentIndex = track.segments.findIndex(s => s.id === segmentId)
      if (segmentIndex !== -1) {
        const originalSegment = track.segments[segmentIndex]
        
        // Calculate split point in source video time
        const segmentDuration = originalSegment.endLoc - originalSegment.startLoc
        const sourceDuration = originalSegment.endTime - originalSegment.startTime
        const splitRatio = (splitTime - originalSegment.startLoc) / segmentDuration
        const sourceSplitTime = originalSegment.startTime + (sourceDuration * splitRatio)
        
        // Create two new segments
        const firstSegment = {
          ...originalSegment,
          id: `segment_${Date.now()}_1`,
          endTime: sourceSplitTime,
          endLoc: splitTime
        }
        
        const secondSegment = {
          ...originalSegment,
          id: `segment_${Date.now()}_2`,
          startTime: sourceSplitTime,
          startLoc: splitTime
        }
        
        // Replace original segment with two new ones
        track.segments.splice(segmentIndex, 1, firstSegment, secondSegment)
        break
      }
    }
  }

  const clearTimeline = () => {
    tracks.value = []
    currentTime.value = 0
    duration.value = 0
    selectedSegment.value = null
    currentPreviewVideo.value = null
    currentCropParams.value = null
  }

  const exportTimelineData = () => {
    return {
      tracks: tracks.value,
      duration: duration.value,
      fps: fps.value,
      currentTime: currentTime.value,
      zoom: zoom.value
    }
  }

  const importTimelineData = (data) => {
    tracks.value = data.tracks || []
    duration.value = data.duration || 0
    currentTime.value = data.currentTime || 0
    zoom.value = data.zoom || 1
    selectedSegment.value = null
    currentPreviewVideo.value = null
    currentCropParams.value = null
  }

  return {
    // State
    tracks,
    currentTime,
    duration,
    zoom,
    isPlaying,
    selectedSegment,
    currentPreviewVideo,
    currentCropParams,
    
    // Getters
    fps,
    frameTime,
    currentFrame,
    
    // Actions
    setCurrentTime,
    setDuration,
    setZoom,
    togglePlayback,
    addTrack,
    removeTrack,
    addSegmentToTrack,
    removeSegment,
    updateSegment,
    selectSegment,
    updateCropParams,
    updatePreviewVideo,
    getSegmentAtTime,
    splitSegment,
    clearTimeline,
    exportTimelineData,
    importTimelineData
  }
})
