from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
import os
import uuid
from typing import Dict, Any
from pydantic import BaseModel

from app.core.video_processor import VideoProcessor

router = APIRouter()
video_processor = VideoProcessor()

class ExportRequest(BaseModel):
    project_data: Dict[str, Any]
    output_settings: Dict[str, Any] = {
        "format": "mp4",
        "quality": "high",
        "resolution": "original"
    }

@router.post("/start")
async def start_export(export_request: ExportRequest, background_tasks: BackgroundTasks):
    """Start video export process"""
    try:
        export_id = str(uuid.uuid4())
        output_filename = f"{export_id}.mp4"
        output_path = os.path.join("exports", output_filename)
        
        # Add export task to background
        background_tasks.add_task(
            video_processor.export_video,
            export_request.project_data,
            output_path,
            export_request.output_settings
        )
        
        return {
            "export_id": export_id,
            "status": "started",
            "message": "Export process started"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{export_id}")
async def get_export_status(export_id: str):
    """Get export status"""
    try:
        output_path = os.path.join("exports", f"{export_id}.mp4")
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            return {
                "export_id": export_id,
                "status": "completed",
                "file_size": file_size,
                "download_url": f"/api/export/download/{export_id}"
            }
        else:
            return {
                "export_id": export_id,
                "status": "processing"
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/download/{export_id}")
async def download_export(export_id: str):
    """Download exported video"""
    try:
        output_path = os.path.join("exports", f"{export_id}.mp4")
        
        if not os.path.exists(output_path):
            raise HTTPException(status_code=404, detail="Export not found")
        
        return FileResponse(
            output_path,
            media_type="video/mp4",
            filename=f"exported_video_{export_id}.mp4"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/list")
async def list_exports():
    """List all exports"""
    try:
        exports = []
        exports_dir = "exports"
        if os.path.exists(exports_dir):
            for filename in os.listdir(exports_dir):
                if filename.endswith('.mp4'):
                    file_path = os.path.join(exports_dir, filename)
                    export_id = os.path.splitext(filename)[0]
                    file_size = os.path.getsize(file_path)
                    
                    exports.append({
                        "export_id": export_id,
                        "filename": filename,
                        "file_size": file_size,
                        "download_url": f"/api/export/download/{export_id}"
                    })
        
        return {"exports": exports}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
