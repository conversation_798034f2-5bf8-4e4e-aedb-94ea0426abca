import { defineStore } from 'pinia'
import { ref } from 'vue'
import axios from 'axios'

const API_BASE = 'http://localhost:8000/api'

export const useMediaStore = defineStore('media', () => {
  // State
  const videos = ref([])
  const loading = ref(false)

  // Actions
  const uploadVideo = async (file) => {
    try {
      loading.value = true
      
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await axios.post(`${API_BASE}/video/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      const videoData = response.data
      videos.value.push(videoData)
      
      return videoData
    } catch (error) {
      console.error('Upload failed:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getVideoList = async () => {
    try {
      loading.value = true
      
      const response = await axios.get(`${API_BASE}/video/list`)
      videos.value = response.data.videos
      
      return videos.value
    } catch (error) {
      console.error('Failed to get video list:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getVideoInfo = async (videoId) => {
    try {
      const response = await axios.get(`${API_BASE}/video/info/${videoId}`)
      return response.data
    } catch (error) {
      console.error('Failed to get video info:', error)
      throw error
    }
  }

  const getVideoById = (videoId) => {
    return videos.value.find(video => video.id === videoId)
  }

  return {
    // State
    videos,
    loading,
    
    // Actions
    uploadVideo,
    getVideoList,
    getVideoInfo,
    getVideoById
  }
})
