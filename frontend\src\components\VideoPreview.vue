<template>
  <div class="video-preview-container">
    <div class="preview-header">
      <NSpace>
        <NButton @click="togglePlay" :type="isPlaying ? 'default' : 'primary'">
          {{ isPlaying ? '暂停' : '播放' }}
        </NButton>
        <NButton @click="toggleCropOverlay" v-if="previewVideo">
          {{ showCropOverlay ? '隐藏裁剪' : '显示裁剪' }}
        </NButton>
        <span>{{ timelineStore.currentTime.toFixed(1) }}s</span>
        <NInputNumber
          v-model:value="timelineCurrentTime"
          :min="0"
          :max="timelineStore.duration"
          :step="0.1"
          size="small"
          style="width: 120px;"
        />
        <span>/ {{ timelineStore.duration.toFixed(1) }}s</span>
      </NSpace>
    </div>
    
    <div class="preview-content" ref="previewContainer">
      <div v-if="previewVideo" class="video-wrapper">
        <div class="video-container">
          <video
            ref="videoElement"
            :src="previewVideo.url"
            @loadedmetadata="onVideoLoaded"
            @timeupdate="onTimeUpdate"
            @click="togglePlay"
          />

          <!-- Crop overlay -->
          <div
            v-if="showCropOverlay && cropParams"
            class="crop-overlay"
            :style="cropOverlayStyle"
            @mousedown="startCropDrag"
          >
            <div class="crop-handles">
              <div class="handle top-left" @mousedown.stop="startResize('top-left')"></div>
              <div class="handle top-right" @mousedown.stop="startResize('top-right')"></div>
              <div class="handle bottom-left" @mousedown.stop="startResize('bottom-left')"></div>
              <div class="handle bottom-right" @mousedown.stop="startResize('bottom-right')"></div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="no-video">
        <NEmpty description="将视频从左侧素材库拖拽到时间轴开始编辑">
          <template #extra>
            <div style="color: #999; font-size: 12px; margin-top: 8px;">
              <p>1. 点击左侧"上传视频"按钮上传视频文件</p>
              <p>2. 将视频拖拽到下方时间轴</p>
              <p>3. 点击时间轴上的视频片段进行预览和编辑</p>
            </div>
          </template>
        </NEmpty>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { NButton, NSpace, NInputNumber, NEmpty } from 'naive-ui'
import { useTimelineStore } from '../stores/timeline'

const timelineStore = useTimelineStore()

const videoElement = ref(null)
const previewContainer = ref(null)
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const showCropOverlay = ref(false)

// Video preview data - should show the segment at current timeline position
const previewVideo = computed(() => {
  // Find the segment at current timeline time
  const currentSegment = timelineStore.getSegmentAtTime(timelineStore.currentTime)
  if (currentSegment) {
    return {
      url: `http://localhost:8000/api/video/file/${currentSegment.sourceVideoId}`,
      segment: currentSegment,
      currentTime: timelineStore.currentTime
    }
  }

  return null
})

const cropParams = computed(() => timelineStore.currentCropParams)

// Timeline time control
const timelineCurrentTime = computed({
  get: () => timelineStore.currentTime,
  set: (value) => timelineStore.setCurrentTime(value)
})

// Crop overlay style
const cropOverlayStyle = computed(() => {
  if (!cropParams.value || !previewContainer.value) return {}
  
  const container = previewContainer.value.getBoundingClientRect()
  return {
    left: `${cropParams.value.x * 100}%`,
    top: `${cropParams.value.y * 100}%`,
    width: `${cropParams.value.width * 100}%`,
    height: `${cropParams.value.height * 100}%`,
  }
})

// Video controls
const togglePlay = () => {
  if (!videoElement.value) return
  
  if (isPlaying.value) {
    videoElement.value.pause()
  } else {
    videoElement.value.play()
  }
  isPlaying.value = !isPlaying.value
}

const onVideoLoaded = () => {
  if (videoElement.value && previewVideo.value) {
    duration.value = videoElement.value.duration
    // Set video time to match the segment's position
    const segment = previewVideo.value.segment
    const segmentTime = timelineStore.currentTime - segment.startLoc + segment.startTime
    videoElement.value.currentTime = Math.max(0, Math.min(segmentTime, segment.endTime))
  }
}

const onTimeUpdate = () => {
  if (videoElement.value && previewVideo.value) {
    const segment = previewVideo.value.segment
    const videoTime = videoElement.value.currentTime

    // Convert video time to timeline time
    const timelineTime = segment.startLoc + (videoTime - segment.startTime)

    // Only update if within segment bounds
    if (timelineTime >= segment.startLoc && timelineTime <= segment.endLoc) {
      timelineStore.setCurrentTime(timelineTime)
    } else if (timelineTime > segment.endLoc) {
      // Move to next segment or pause
      const nextSegment = findNextSegment(segment.endLoc)
      if (nextSegment) {
        timelineStore.setCurrentTime(nextSegment.startLoc)
      } else {
        // End of timeline, pause
        if (videoElement.value) {
          videoElement.value.pause()
          isPlaying.value = false
        }
      }
    }
  }
}

// Helper function to find next segment
const findNextSegment = (currentTime) => {
  let nextSegment = null
  let minStartTime = Infinity

  for (const track of timelineStore.tracks) {
    for (const segment of track.segments) {
      if (segment.startLoc > currentTime && segment.startLoc < minStartTime) {
        nextSegment = segment
        minStartTime = segment.startLoc
      }
    }
  }

  return nextSegment
}

// Watch for timeline changes
watch(() => timelineStore.currentTime, (newTime) => {
  if (videoElement.value && previewVideo.value) {
    const segment = previewVideo.value.segment
    // Convert timeline time to video time
    const videoTime = newTime - segment.startLoc + segment.startTime

    // Only update if the calculated time is within the segment bounds
    if (videoTime >= segment.startTime && videoTime <= segment.endTime) {
      if (Math.abs(videoElement.value.currentTime - videoTime) > 0.1) {
        videoElement.value.currentTime = videoTime
        currentTime.value = videoTime
      }
    }
  }
})

// Watch for segment changes (when timeline position moves to different segment)
watch(() => previewVideo.value?.segment?.id, (newSegmentId, oldSegmentId) => {
  if (newSegmentId !== oldSegmentId && videoElement.value && previewVideo.value) {
    // Segment changed, update video source and time
    const segment = previewVideo.value.segment
    const videoTime = timelineStore.currentTime - segment.startLoc + segment.startTime
    videoElement.value.currentTime = Math.max(0, Math.min(videoTime, segment.endTime))
  }
})

// Crop functionality
let isDragging = false
let isResizing = false
let dragStart = { x: 0, y: 0 }
let resizeMode = ''

const startCropDrag = (event) => {
  if (isResizing) return
  isDragging = true
  dragStart = { x: event.clientX, y: event.clientY }
  document.addEventListener('mousemove', onCropDrag)
  document.addEventListener('mouseup', stopCropDrag)
}

const onCropDrag = (event) => {
  if (!isDragging || !cropParams.value || !previewContainer.value) return
  
  const container = previewContainer.value.getBoundingClientRect()
  const deltaX = (event.clientX - dragStart.x) / container.width
  const deltaY = (event.clientY - dragStart.y) / container.height
  
  const newParams = { ...cropParams.value }
  newParams.x = Math.max(0, Math.min(1 - newParams.width, newParams.x + deltaX))
  newParams.y = Math.max(0, Math.min(1 - newParams.height, newParams.y + deltaY))
  
  timelineStore.updateCropParams(newParams)
  dragStart = { x: event.clientX, y: event.clientY }
}

const stopCropDrag = () => {
  isDragging = false
  document.removeEventListener('mousemove', onCropDrag)
  document.removeEventListener('mouseup', stopCropDrag)
}

const startResize = (mode) => {
  isResizing = true
  resizeMode = mode
  document.addEventListener('mousemove', onCropResize)
  document.addEventListener('mouseup', stopCropResize)
}

const onCropResize = (event) => {
  // Implement resize logic based on resizeMode
  // This is a simplified version
}

const stopCropResize = () => {
  isResizing = false
  resizeMode = ''
  document.removeEventListener('mousemove', onCropResize)
  document.removeEventListener('mouseup', stopCropResize)
}

// Toggle crop overlay
const toggleCropOverlay = () => {
  showCropOverlay.value = !showCropOverlay.value
  if (showCropOverlay.value && !cropParams.value) {
    // Initialize default crop params
    timelineStore.updateCropParams({
      x: 0.1,
      y: 0.1,
      width: 0.8,
      height: 0.8
    })
  }
}

onMounted(() => {
  // Initialize component
})

onUnmounted(() => {
  // Cleanup
  document.removeEventListener('mousemove', onCropDrag)
  document.removeEventListener('mouseup', stopCropDrag)
  document.removeEventListener('mousemove', onCropResize)
  document.removeEventListener('mouseup', stopCropResize)
})
</script>

<style scoped>
.video-preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
  background: #fafafa;
}

.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  position: relative;
  overflow: hidden;
  padding: 20px;
}

.video-wrapper {
  width: 100%;
  height: 100%;
  max-width: 800px;
  max-height: 450px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  overflow: hidden;
}

.video-container video {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  display: block;
  border-radius: 4px;
}

.no-video {
  color: white;
}

.crop-overlay {
  position: absolute;
  border: 2px solid #18a058;
  background: rgba(24, 160, 88, 0.1);
  cursor: move;
}

.crop-handles {
  position: relative;
  width: 100%;
  height: 100%;
}

.handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #18a058;
  border: 1px solid white;
  cursor: pointer;
}

.handle.top-left {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

.handle.top-right {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.handle.bottom-left {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.handle.bottom-right {
  bottom: -4px;
  right: -4px;
  cursor: se-resize;
}
</style>
