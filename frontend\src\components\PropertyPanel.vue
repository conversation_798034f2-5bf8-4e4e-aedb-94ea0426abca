<template>
  <div class="property-panel">
    <div class="panel-header">
      <h3>属性面板</h3>
    </div>
    
    <div class="panel-content">
      <div v-if="!selectedSegment" class="no-selection">
        <NEmpty description="选择一个视频片段查看属性" />
      </div>
      
      <div v-else class="properties">
        <!-- Basic Info -->
        <div class="property-group">
          <h4>基本信息</h4>
          <div class="property-item">
            <label>片段ID:</label>
            <span>{{ selectedSegment.id }}</span>
          </div>
          <div class="property-item">
            <label>源视频:</label>
            <span>{{ selectedSegment.sourceVideoId }}</span>
          </div>
        </div>
        
        <!-- Time Properties -->
        <div class="property-group">
          <h4>时间属性</h4>
          <div class="property-item">
            <label>源视频开始时间:</label>
            <NInputNumber 
              v-model:value="timeProps.startTime"
              :min="0"
              :step="0.1"
              size="small"
              @update:value="updateTimeProperty('startTime', $event)"
            />
          </div>
          <div class="property-item">
            <label>源视频结束时间:</label>
            <NInputNumber 
              v-model:value="timeProps.endTime"
              :min="timeProps.startTime + 0.1"
              :step="0.1"
              size="small"
              @update:value="updateTimeProperty('endTime', $event)"
            />
          </div>
          <div class="property-item">
            <label>时间轴开始位置:</label>
            <NInputNumber 
              v-model:value="timeProps.startLoc"
              :min="0"
              :step="0.1"
              size="small"
              @update:value="updateTimeProperty('startLoc', $event)"
            />
          </div>
          <div class="property-item">
            <label>时间轴结束位置:</label>
            <NInputNumber 
              v-model:value="timeProps.endLoc"
              :min="timeProps.startLoc + 0.1"
              :step="0.1"
              size="small"
              @update:value="updateTimeProperty('endLoc', $event)"
            />
          </div>
        </div>
        
        <!-- Crop Properties -->
        <div class="property-group">
          <h4>裁剪属性</h4>
          <div class="property-item">
            <NCheckbox 
              :checked="!!cropProps"
              @update:checked="toggleCrop"
            >
              启用裁剪
            </NCheckbox>
          </div>
          
          <template v-if="cropProps">
            <div class="property-item">
              <label>X位置 (%):</label>
              <NInputNumber 
                v-model:value="cropProps.x"
                :min="0"
                :max="1"
                :step="0.01"
                :precision="2"
                size="small"
                @update:value="updateCropProperty('x', $event)"
              />
            </div>
            <div class="property-item">
              <label>Y位置 (%):</label>
              <NInputNumber 
                v-model:value="cropProps.y"
                :min="0"
                :max="1"
                :step="0.01"
                :precision="2"
                size="small"
                @update:value="updateCropProperty('y', $event)"
              />
            </div>
            <div class="property-item">
              <label>宽度 (%):</label>
              <NInputNumber 
                v-model:value="cropProps.width"
                :min="0.01"
                :max="1"
                :step="0.01"
                :precision="2"
                size="small"
                @update:value="updateCropProperty('width', $event)"
              />
            </div>
            <div class="property-item">
              <label>高度 (%):</label>
              <NInputNumber 
                v-model:value="cropProps.height"
                :min="0.01"
                :max="1"
                :step="0.01"
                :precision="2"
                size="small"
                @update:value="updateCropProperty('height', $event)"
              />
            </div>
            
            <div class="property-item">
              <NButton size="small" @click="resetCrop">重置裁剪</NButton>
            </div>
          </template>
        </div>
        
        <!-- Actions -->
        <div class="property-group">
          <h4>操作</h4>
          <div class="property-item">
            <NSpace>
              <NButton size="small" @click="splitSegment">分割片段</NButton>
              <NButton size="small" type="error" @click="deleteSegment">删除片段</NButton>
            </NSpace>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { NEmpty, NInputNumber, NCheckbox, NButton, NSpace } from 'naive-ui'
import { useTimelineStore } from '../stores/timeline'

const timelineStore = useTimelineStore()

const selectedSegment = computed(() => timelineStore.selectedSegment)

// Reactive properties
const timeProps = ref({
  startTime: 0,
  endTime: 0,
  startLoc: 0,
  endLoc: 0
})

const cropProps = ref(null)

// Watch for segment changes
watch(selectedSegment, (newSegment) => {
  if (newSegment) {
    timeProps.value = {
      startTime: newSegment.startTime,
      endTime: newSegment.endTime,
      startLoc: newSegment.startLoc,
      endLoc: newSegment.endLoc
    }
    cropProps.value = newSegment.cropParams ? { ...newSegment.cropParams } : null
  }
}, { immediate: true })

// Update methods
const updateTimeProperty = (property, value) => {
  if (selectedSegment.value && value !== null && value !== undefined) {
    timelineStore.updateSegment(selectedSegment.value.id, {
      [property]: value
    })
  }
}

const updateCropProperty = (property, value) => {
  if (selectedSegment.value && cropProps.value && value !== null && value !== undefined) {
    const newCropParams = { ...cropProps.value, [property]: value }
    cropProps.value = newCropParams
    timelineStore.updateCropParams(newCropParams)
  }
}

const toggleCrop = (enabled) => {
  if (selectedSegment.value) {
    if (enabled) {
      const defaultCrop = { x: 0, y: 0, width: 1, height: 1 }
      cropProps.value = defaultCrop
      timelineStore.updateCropParams(defaultCrop)
    } else {
      cropProps.value = null
      timelineStore.updateCropParams(null)
    }
  }
}

const resetCrop = () => {
  if (selectedSegment.value) {
    const defaultCrop = { x: 0, y: 0, width: 1, height: 1 }
    cropProps.value = defaultCrop
    timelineStore.updateCropParams(defaultCrop)
  }
}

const splitSegment = () => {
  if (selectedSegment.value) {
    const currentTime = timelineStore.currentTime
    const segment = selectedSegment.value
    
    if (currentTime > segment.startLoc && currentTime < segment.endLoc) {
      timelineStore.splitSegment(segment.id, currentTime)
    }
  }
}

const deleteSegment = () => {
  if (selectedSegment.value) {
    timelineStore.removeSegment(selectedSegment.value.id)
  }
}
</script>

<style scoped>
.property-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.properties {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.property-group {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
}

.property-group h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.property-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  gap: 8px;
}

.property-item:last-child {
  margin-bottom: 0;
}

.property-item label {
  font-size: 12px;
  color: #666;
  min-width: 80px;
  text-align: left;
}

.property-item span {
  font-size: 12px;
  color: #333;
  word-break: break-all;
}
</style>
