import imageio
import os
from PIL import Image
import numpy as np

class ThumbnailGenerator:
    def __init__(self):
        self.thumbnail_size = (160, 90)  # 16:9 aspect ratio
    
    async def generate_thumbnail(self, video_path: str, video_id: str) -> str:
        """Generate thumbnail for video"""
        try:
            thumbnail_path = os.path.join("thumbnails", f"{video_id}.jpg")
            
            # Extract frame from middle of video
            reader = imageio.get_reader(video_path)
            meta = reader.get_meta_data()
            frame_count = meta.get('nframes', 0)
            
            if frame_count > 0:
                middle_frame = frame_count // 2
                frame = reader.get_data(middle_frame)
            else:
                # If frame count is unknown, try to get first frame
                frame = reader.get_data(0)
            
            reader.close()
            
            # Convert to PIL Image and resize
            image = Image.fromarray(frame)
            image.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)
            
            # Save thumbnail
            image.save(thumbnail_path, "JPEG", quality=85)
            
            return thumbnail_path
        except Exception as e:
            # Create a default thumbnail if video processing fails
            return self._create_default_thumbnail(video_id)
    
    def _create_default_thumbnail(self, video_id: str) -> str:
        """Create a default thumbnail when video processing fails"""
        try:
            thumbnail_path = os.path.join("thumbnails", f"{video_id}.jpg")
            
            # Create a simple gray image with text
            image = Image.new('RGB', self.thumbnail_size, color='gray')
            image.save(thumbnail_path, "JPEG", quality=85)
            
            return thumbnail_path
        except Exception:
            return None
    
    def generate_timeline_thumbnails(self, video_path: str, video_id: str, 
                                   duration: float, interval: float = 1.0) -> list:
        """Generate thumbnails for timeline scrubbing"""
        try:
            thumbnails = []
            reader = imageio.get_reader(video_path)
            meta = reader.get_meta_data()
            fps = meta.get('fps', 30)
            
            current_time = 0
            while current_time < duration:
                frame_number = int(current_time * fps)
                
                try:
                    frame = reader.get_data(frame_number)
                    
                    # Create thumbnail
                    image = Image.fromarray(frame)
                    image.thumbnail((80, 45), Image.Resampling.LANCZOS)  # Smaller for timeline
                    
                    thumbnail_filename = f"{video_id}_timeline_{current_time:.1f}.jpg"
                    thumbnail_path = os.path.join("thumbnails", thumbnail_filename)
                    image.save(thumbnail_path, "JPEG", quality=75)
                    
                    thumbnails.append({
                        "time": current_time,
                        "path": thumbnail_path
                    })
                except:
                    # Skip this frame if there's an error
                    pass
                
                current_time += interval
            
            reader.close()
            return thumbnails
        except Exception as e:
            return []
