<script setup>
import { ref } from 'vue'
import { NLayout, NLayoutHeader, NLayoutContent, NH1, NButton } from 'naive-ui'

const message = ref('AI Video Editor - 基础架构已搭建完成!')
</script>

<template>
  <NLayout style="height: 100vh;">
    <!-- Header -->
    <NLayoutHeader style="height: 60px; padding: 0 24px; border-bottom: 1px solid #e0e0e0;">
      <div style="display: flex; align-items: center; height: 100%;">
        <NH1 style="margin: 0; font-size: 20px;">{{ message }}</NH1>
        <div style="margin-left: auto;">
          <NButton type="primary">测试按钮</NButton>
        </div>
      </div>
    </NLayoutHeader>

    <!-- Main Content -->
    <NLayoutContent style="height: calc(100vh - 60px); display: flex; align-items: center; justify-content: center;">
      <div style="text-align: center;">
        <h2>🎉 视频编辑器基础架构搭建完成！</h2>
        <p>前端: Vue3 + Naive UI</p>
        <p>后端: FastAPI + imageio_ffmpeg</p>
        <p>状态管理: Pinia</p>
        <p>视频代理系统已实现</p>
        <br>
        <p>后端API运行在: <a href="http://localhost:8000" target="_blank">http://localhost:8000</a></p>
        <p>前端应用运行在: <a href="http://localhost:5173" target="_blank">http://localhost:5173</a></p>
      </div>
    </NLayoutContent>
  </NLayout>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#app {
  height: 100vh;
  overflow: hidden;
}
</style>
