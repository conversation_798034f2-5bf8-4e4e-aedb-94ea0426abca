<script setup>
import { ref, onMounted } from 'vue'
import { NLayout, NLayoutHeader, NLayoutContent, NLayoutSider, NH1, NButton, NSpace } from 'naive-ui'
import VideoPreview from './components/VideoPreview.vue'
import Timeline from './components/Timeline.vue'
import MediaLibrary from './components/MediaLibrary.vue'
import PropertyPanel from './components/PropertyPanel.vue'
import ProjectManager from './components/ProjectManager.vue'
import { useTimelineStore } from './stores/timeline'

const showProjectManager = ref(false)
const timelineStore = useTimelineStore()

const handleExport = () => {
  // TODO: Implement export functionality
  console.log('Export video clicked')
}

onMounted(() => {
  // Initialize the application with a default track
  timelineStore.addTrack()
})
</script>

<template>
  <NLayout style="height: 100vh;">
    <!-- Header -->
    <NLayoutHeader style="height: 60px; padding: 0 24px; border-bottom: 1px solid #e0e0e0;">
      <div style="display: flex; align-items: center; height: 100%;">
        <NH1 style="margin: 0; font-size: 20px;">AI Video Editor</NH1>
        <div style="margin-left: auto;">
          <NSpace>
            <NButton @click="showProjectManager = true">项目管理</NButton>
            <NButton type="primary" @click="handleExport">导出视频</NButton>
          </NSpace>
        </div>
      </div>
    </NLayoutHeader>

    <!-- Main Content -->
    <NLayoutContent style="height: calc(100vh - 60px);">
      <NLayout has-sider style="height: 100%;">
        <!-- Left Sidebar - Media Library -->
        <NLayoutSider
          width="300"
          style="border-right: 1px solid #e0e0e0;"
          :native-scrollbar="false"
        >
          <MediaLibrary />
        </NLayoutSider>

        <!-- Center Content -->
        <NLayoutContent style="display: flex; flex-direction: column;">
          <!-- Video Preview -->
          <div style="flex: 1; min-height: 0; border-bottom: 1px solid #e0e0e0;">
            <VideoPreview />
          </div>

          <!-- Timeline -->
          <div style="height: 300px;">
            <Timeline />
          </div>
        </NLayoutContent>

        <!-- Right Sidebar - Property Panel -->
        <NLayoutSider
          width="300"
          style="border-left: 1px solid #e0e0e0;"
          :native-scrollbar="false"
        >
          <PropertyPanel />
        </NLayoutSider>
      </NLayout>
    </NLayoutContent>

    <!-- Project Manager Modal -->
    <ProjectManager v-model:show="showProjectManager" />
  </NLayout>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#app {
  height: 100vh;
  overflow: hidden;
}
</style>
