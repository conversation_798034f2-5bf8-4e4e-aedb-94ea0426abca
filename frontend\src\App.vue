<script setup>
import { ref, onMounted } from 'vue'
import { NLayout, NLayoutHeader, NLayoutContent, NLayoutSider, NH1, NButton, NSpace, NCard } from 'naive-ui'

// 先注释掉组件导入，逐步测试
// import VideoPreview from './components/VideoPreview.vue'
// import Timeline from './components/Timeline.vue'
// import MediaLibrary from './components/MediaLibrary.vue'
// import PropertyPanel from './components/PropertyPanel.vue'
// import ProjectManager from './components/ProjectManager.vue'
// import { useTimelineStore } from './stores/timeline'

const showProjectManager = ref(false)
// const timelineStore = useTimelineStore()

const handleExport = () => {
  console.log('Export video clicked')
}

onMounted(() => {
  console.log('App mounted')
  // timelineStore.addTrack()
})
</script>

<template>
  <NLayout style="height: 100vh; width: 100vw;">
    <!-- <PERSON><PERSON> -->
    <NLayoutHeader style="height: 60px; padding: 0 24px; border-bottom: 1px solid #e0e0e0; background: white;">
      <div style="display: flex; align-items: center; height: 100%;">
        <NH1 style="margin: 0; font-size: 20px;">AI Video Editor - 测试版</NH1>
        <div style="margin-left: auto;">
          <NSpace>
            <NButton @click="showProjectManager = true">项目管理</NButton>
            <NButton type="primary" @click="handleExport">导出视频</NButton>
          </NSpace>
        </div>
      </div>
    </NLayoutHeader>

    <!-- Main Content -->
    <NLayoutContent style="height: calc(100vh - 60px); background: #f5f5f5;">
      <NLayout has-sider style="height: 100%;">
        <!-- Left Sidebar -->
        <NLayoutSider
          width="300"
          style="border-right: 1px solid #e0e0e0; background: white;"
          :native-scrollbar="false"
        >
          <NCard title="素材库" style="height: 100%; border-radius: 0;">
            <p>素材库组件位置</p>
            <NButton type="primary">上传视频</NButton>
          </NCard>
        </NLayoutSider>

        <!-- Center Content -->
        <NLayoutContent style="display: flex; flex-direction: column;">
          <!-- Video Preview -->
          <div style="flex: 1; min-height: 0; border-bottom: 1px solid #e0e0e0; background: white;">
            <NCard title="视频预览" style="height: 100%; border-radius: 0;">
              <div style="height: 300px; background: #000; display: flex; align-items: center; justify-content: center; color: white;">
                视频预览区域
              </div>
            </NCard>
          </div>

          <!-- Timeline -->
          <div style="height: 300px; background: white;">
            <NCard title="时间轴" style="height: 100%; border-radius: 0;">
              <div style="height: 200px; background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
                时间轴区域
              </div>
            </NCard>
          </div>
        </NLayoutContent>

        <!-- Right Sidebar -->
        <NLayoutSider
          width="300"
          style="border-left: 1px solid #e0e0e0; background: white;"
          :native-scrollbar="false"
        >
          <NCard title="属性面板" style="height: 100%; border-radius: 0;">
            <p>属性面板组件位置</p>
          </NCard>
        </NLayoutSider>
      </NLayout>
    </NLayoutContent>
  </NLayout>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#app {
  height: 100vh;
  overflow: hidden;
}
</style>
