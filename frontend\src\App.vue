<script setup>
import { ref, onMounted } from 'vue'
import { NLayout, NLayoutHeader, NLayoutContent, NLayoutSider, NH1, NButton, NSpace, NCard } from 'naive-ui'

// 逐步恢复组件
import MediaLibrary from './components/MediaLibrary.vue'
import Timeline from './components/Timeline.vue'
import VideoPreview from './components/VideoPreview.vue'
import PropertyPanel from './components/PropertyPanel.vue'
import ProjectManager from './components/ProjectManager.vue'
import { useTimelineStore } from './stores/timeline'

const showProjectManager = ref(false)
const timelineStore = useTimelineStore()

const handleExport = () => {
  console.log('Export video clicked')
}

onMounted(() => {
  console.log('App mounted')
  timelineStore.addTrack()
})
</script>

<template>
  <NLayout style="height: 100vh; width: 100vw; position: fixed; top: 0; left: 0;">
    <!-- Header -->
    <NLayoutHeader style="height: 60px; padding: 0 24px; border-bottom: 1px solid #e0e0e0; background: white; width: 100%;">
      <div style="display: flex; align-items: center; height: 100%; width: 100%;">
        <NH1 style="margin: 0; font-size: 20px;">AI Video Editor</NH1>
        <div style="margin-left: auto;">
          <NSpace>
            <NButton @click="showProjectManager = true">项目管理</NButton>
            <NButton type="primary" @click="handleExport">导出视频</NButton>
          </NSpace>
        </div>
      </div>
    </NLayoutHeader>

    <!-- Main Content -->
    <NLayoutContent style="height: calc(100vh - 60px); background: #f5f5f5; width: 100%;">
      <NLayout has-sider style="height: 100%; width: 100%;">
        <!-- Left Sidebar -->
        <NLayoutSider
          width="300"
          style="border-right: 1px solid #e0e0e0; background: white; height: 100%;"
          :native-scrollbar="false"
        >
          <MediaLibrary />
        </NLayoutSider>

        <!-- Center Content -->
        <NLayoutContent style="display: flex; flex-direction: column; flex: 1; min-width: 0;">
          <!-- Video Preview -->
          <div style="flex: 1; min-height: 0; border-bottom: 1px solid #e0e0e0; background: white;">
            <VideoPreview />
          </div>

          <!-- Timeline -->
          <div style="height: 300px; background: white;">
            <Timeline />
          </div>
        </NLayoutContent>

        <!-- Right Sidebar -->
        <NLayoutSider
          width="300"
          style="border-left: 1px solid #e0e0e0; background: white; height: 100%;"
          :native-scrollbar="false"
        >
          <PropertyPanel />
        </NLayoutSider>
      </NLayout>
    </NLayoutContent>

    <!-- Project Manager Modal -->
    <ProjectManager v-model:show="showProjectManager" />
  </NLayout>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* 确保Naive UI的Layout组件占满全屏 */
.n-layout {
  height: 100vh !important;
  width: 100vw !important;
}

.n-layout-content {
  width: 100% !important;
}

.n-layout-sider {
  flex-shrink: 0 !important;
}
</style>
