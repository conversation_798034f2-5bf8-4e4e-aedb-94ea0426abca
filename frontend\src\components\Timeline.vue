<template>
  <div class="timeline-container">
    <div class="timeline-header">
      <div class="timeline-controls">
        <NSpace>
          <NButton size="small" @click="addTrack">添加轨道</NButton>
          <NButton size="small" @click="zoomIn">放大</NButton>
          <NButton size="small" @click="zoomOut">缩小</NButton>
          <span>缩放: {{ (zoom * 100).toFixed(0) }}%</span>
        </NSpace>
      </div>
      
      <div class="time-ruler" ref="timeRuler" @click="seekToTime">
        <div 
          v-for="mark in timeMarks" 
          :key="mark.time"
          class="time-mark"
          :style="{ left: mark.position + 'px' }"
        >
          <div class="time-label">{{ formatTime(mark.time) }}</div>
        </div>
        
        <!-- Playhead -->
        <div 
          class="playhead"
          :style="{ left: playheadPosition + 'px' }"
        ></div>
      </div>
    </div>
    
    <div 
      class="timeline-content" 
      ref="timelineContent"
      @wheel="onWheel"
      @scroll="onScroll"
    >
      <div class="tracks-container">
        <TrackContainer 
          v-for="track in tracks"
          :key="track.id"
          :track="track"
          :zoom="zoom"
          :scroll-left="scrollLeft"
          @segment-select="onSegmentSelect"
          @segment-update="onSegmentUpdate"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { NButton, NSpace } from 'naive-ui'
import TrackContainer from './TrackContainer.vue'
import { useTimelineStore } from '../stores/timeline'

const timelineStore = useTimelineStore()

const timeRuler = ref(null)
const timelineContent = ref(null)
const scrollLeft = ref(0)

// Timeline state
const tracks = computed(() => timelineStore.tracks)
const currentTime = computed(() => timelineStore.currentTime)
const duration = computed(() => timelineStore.duration)
const zoom = computed(() => timelineStore.zoom)

// Time ruler calculations
const pixelsPerSecond = computed(() => 50 * zoom.value)
const timeMarks = computed(() => {
  const marks = []
  const maxDuration = Math.max(duration.value, 60) // At least 60 seconds
  const interval = zoom.value > 2 ? 0.5 : zoom.value > 1 ? 1 : zoom.value > 0.5 ? 2 : 5

  for (let time = 0; time <= maxDuration; time += interval) {
    marks.push({
      time,
      position: time * pixelsPerSecond.value
    })
  }
  return marks
})

const playheadPosition = computed(() => {
  return currentTime.value * pixelsPerSecond.value - scrollLeft.value
})

// Timeline controls
const addTrack = () => {
  timelineStore.addTrack()
}

const zoomIn = () => {
  timelineStore.setZoom(Math.min(zoom.value * 1.5, 10))
}

const zoomOut = () => {
  timelineStore.setZoom(Math.max(zoom.value / 1.5, 0.1))
}

const seekToTime = (event) => {
  if (!timeRuler.value) return

  const rect = timeRuler.value.getBoundingClientRect()
  const x = event.clientX - rect.left + scrollLeft.value
  const time = x / pixelsPerSecond.value
  const maxDuration = Math.max(duration.value, 60)

  timelineStore.setCurrentTime(Math.max(0, Math.min(time, maxDuration)))
}

// Scroll and zoom handling
const onWheel = (event) => {
  event.preventDefault()
  
  if (event.altKey) {
    // Alt + wheel = zoom
    const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1
    timelineStore.setZoom(Math.max(0.1, Math.min(10, zoom.value * zoomFactor)))
  } else if (event.shiftKey) {
    // Shift + wheel = horizontal scroll
    if (timelineContent.value) {
      timelineContent.value.scrollLeft += event.deltaY
    }
  } else {
    // Normal wheel = vertical scroll
    if (timelineContent.value) {
      timelineContent.value.scrollTop += event.deltaY
    }
  }
}

const onScroll = () => {
  if (timelineContent.value) {
    scrollLeft.value = timelineContent.value.scrollLeft
  }
}

// Segment handling
const onSegmentSelect = (segment) => {
  timelineStore.selectSegment(segment)
}

const onSegmentUpdate = (segmentId, updates) => {
  timelineStore.updateSegment(segmentId, updates)
}

// Utility functions
const formatTime = (time) => {
  const minutes = Math.floor(time / 60)
  const seconds = (time % 60).toFixed(1)
  return `${minutes}:${seconds.padStart(4, '0')}`
}

// Keyboard shortcuts
const onKeyDown = (event) => {
  if (event.code === 'Space') {
    event.preventDefault()
    // Toggle play/pause
    timelineStore.togglePlayback()
  } else if (event.code === 'ArrowLeft') {
    event.preventDefault()
    timelineStore.setCurrentTime(Math.max(0, currentTime.value - 0.1))
  } else if (event.code === 'ArrowRight') {
    event.preventDefault()
    timelineStore.setCurrentTime(Math.min(duration.value, currentTime.value + 0.1))
  }
}

onMounted(() => {
  document.addEventListener('keydown', onKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', onKeyDown)
})
</script>

<style scoped>
.timeline-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.timeline-header {
  border-bottom: 1px solid #e0e0e0;
  background: white;
}

.timeline-controls {
  padding: 8px 12px;
  border-bottom: 1px solid #e0e0e0;
}

.time-ruler {
  height: 30px;
  position: relative;
  background: #fafafa;
  border-bottom: 1px solid #e0e0e0;
  overflow: hidden;
  cursor: pointer;
}

.time-mark {
  position: absolute;
  top: 0;
  height: 100%;
  border-left: 1px solid #d0d0d0;
}

.time-label {
  position: absolute;
  top: 2px;
  left: 4px;
  font-size: 11px;
  color: #666;
  white-space: nowrap;
}

.playhead {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background: #e74c3c;
  pointer-events: none;
  z-index: 10;
}

.timeline-content {
  flex: 1;
  overflow: auto;
  background: #f0f0f0;
}

.tracks-container {
  min-height: 100%;
  position: relative;
}
</style>
