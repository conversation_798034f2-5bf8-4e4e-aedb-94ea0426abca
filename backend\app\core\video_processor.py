import imageio_ffmpeg as ffmpeg
import imageio
import numpy as np
import os
from typing import Dict, Any, List

class VideoProcessor:
    def __init__(self):
        self.ffmpeg_path = ffmpeg.get_ffmpeg_exe()
    
    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """Get video information using imageio"""
        try:
            reader = imageio.get_reader(video_path)
            meta = reader.get_meta_data()
            
            return {
                "duration": meta.get('duration', 0),
                "fps": meta.get('fps', 30),
                "width": meta.get('size', [0, 0])[0],
                "height": meta.get('size', [0, 0])[1],
                "frame_count": meta.get('nframes', 0)
            }
        except Exception as e:
            raise Exception(f"Error getting video info: {str(e)}")
    
    def extract_frame(self, video_path: str, timestamp: float) -> np.ndarray:
        """Extract a frame at specific timestamp"""
        try:
            reader = imageio.get_reader(video_path)
            fps = reader.get_meta_data().get('fps', 30)
            frame_number = int(timestamp * fps)
            
            if frame_number >= reader.count_frames():
                frame_number = reader.count_frames() - 1
            
            frame = reader.get_data(frame_number)
            reader.close()
            return frame
        except Exception as e:
            raise Exception(f"Error extracting frame: {str(e)}")
    
    def create_video_proxy(self, video_path: str, start_time: float, end_time: float, 
                          crop_params: Dict[str, float] = None) -> np.ndarray:
        """Create video proxy with time and crop parameters"""
        try:
            reader = imageio.get_reader(video_path)
            meta = reader.get_meta_data()
            fps = meta.get('fps', 30)
            
            start_frame = int(start_time * fps)
            end_frame = int(end_time * fps)
            
            frames = []
            for i in range(start_frame, min(end_frame, reader.count_frames())):
                frame = reader.get_data(i)
                
                # Apply crop if specified
                if crop_params:
                    frame = self._apply_crop(frame, crop_params)
                
                frames.append(frame)
            
            reader.close()
            return np.array(frames)
        except Exception as e:
            raise Exception(f"Error creating video proxy: {str(e)}")
    
    def _apply_crop(self, frame: np.ndarray, crop_params: Dict[str, float]) -> np.ndarray:
        """Apply crop parameters to frame"""
        height, width = frame.shape[:2]
        
        # Get crop parameters (normalized 0-1)
        x = int(crop_params.get('x', 0) * width)
        y = int(crop_params.get('y', 0) * height)
        crop_width = int(crop_params.get('width', 1) * width)
        crop_height = int(crop_params.get('height', 1) * height)
        
        # Ensure crop bounds are valid
        x = max(0, min(x, width - 1))
        y = max(0, min(y, height - 1))
        crop_width = max(1, min(crop_width, width - x))
        crop_height = max(1, min(crop_height, height - y))
        
        return frame[y:y+crop_height, x:x+crop_width]
    
    def export_video(self, project_data: Dict[str, Any], output_path: str, 
                    settings: Dict[str, Any] = None):
        """Export final video based on project data"""
        try:
            # This is a simplified version - in a real implementation,
            # you would process all tracks and segments according to the timeline
            
            tracks = project_data.get('tracks', [])
            if not tracks:
                raise Exception("No tracks found in project")
            
            # For now, just process the first track
            first_track = tracks[0]
            segments = first_track.get('segments', [])
            
            if not segments:
                raise Exception("No segments found in track")
            
            # Create temporary files for each segment
            temp_files = []
            for i, segment in enumerate(segments):
                temp_file = f"temp_segment_{i}.mp4"
                self._export_segment(segment, temp_file)
                temp_files.append(temp_file)
            
            # Concatenate all segments
            self._concatenate_videos(temp_files, output_path)
            
            # Clean up temporary files
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    
        except Exception as e:
            raise Exception(f"Error exporting video: {str(e)}")
    
    def _export_segment(self, segment: Dict[str, Any], output_path: str):
        """Export a single segment"""
        source_video_id = segment.get('sourceVideoId')
        start_time = segment.get('startTime', 0)
        end_time = segment.get('endTime', 0)
        
        # Find source video file
        source_path = None
        for ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv']:
            potential_path = f"uploads/{source_video_id}{ext}"
            if os.path.exists(potential_path):
                source_path = potential_path
                break
        
        if not source_path:
            raise Exception(f"Source video not found: {source_video_id}")
        
        # Use ffmpeg to extract segment
        cmd = [
            self.ffmpeg_path,
            '-i', source_path,
            '-ss', str(start_time),
            '-t', str(end_time - start_time),
            '-c', 'copy',
            '-y',
            output_path
        ]
        
        import subprocess
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"FFmpeg error: {result.stderr}")
    
    def _concatenate_videos(self, input_files: List[str], output_path: str):
        """Concatenate multiple video files"""
        # Create concat file
        concat_file = "concat_list.txt"
        with open(concat_file, 'w') as f:
            for file in input_files:
                f.write(f"file '{file}'\n")
        
        # Use ffmpeg to concatenate
        cmd = [
            self.ffmpeg_path,
            '-f', 'concat',
            '-safe', '0',
            '-i', concat_file,
            '-c', 'copy',
            '-y',
            output_path
        ]
        
        import subprocess
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        # Clean up concat file
        if os.path.exists(concat_file):
            os.remove(concat_file)
        
        if result.returncode != 0:
            raise Exception(f"FFmpeg concatenation error: {result.stderr}")
