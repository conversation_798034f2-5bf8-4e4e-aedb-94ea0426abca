from pydantic import BaseModel
from typing import Dict, Any, List, Optional
from datetime import datetime

class VideoSegment(BaseModel):
    id: str
    sourceVideoId: str
    startTime: float  # Source video start time
    endTime: float    # Source video end time
    startLoc: float   # Timeline start location
    endLoc: float     # Timeline end location
    cropParams: Optional[Dict[str, float]] = None  # x, y, width, height (normalized 0-1)
    
class Track(BaseModel):
    id: str
    name: str
    type: str = "video"  # video, audio
    segments: List[VideoSegment] = []
    enabled: bool = True
    
class Timeline(BaseModel):
    duration: float = 0
    fps: float = 30
    resolution: Dict[str, int] = {"width": 1920, "height": 1080}
    currentTime: float = 0
    zoom: float = 1.0
    
class Project(BaseModel):
    id: str
    name: str
    timeline: Timeline
    tracks: List[Track] = []
    settings: Dict[str, Any] = {}
    created_at: datetime = datetime.now()
    updated_at: datetime = datetime.now()
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
