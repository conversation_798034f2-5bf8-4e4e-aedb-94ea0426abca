from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
import os
import json
import uuid
from typing import Dict, Any
from pydantic import BaseModel

from app.models.project import Project

router = APIRouter()

class ProjectData(BaseModel):
    name: str
    timeline: Dict[str, Any]
    tracks: list
    settings: Dict[str, Any] = {}

@router.post("/create")
async def create_project(project_data: ProjectData):
    """Create a new project"""
    try:
        project_id = str(uuid.uuid4())
        project = Project(
            id=project_id,
            name=project_data.name,
            timeline=project_data.timeline,
            tracks=project_data.tracks,
            settings=project_data.settings
        )
        
        # Save project to file
        project_path = os.path.join("projects", f"{project_id}.json")
        with open(project_path, "w", encoding="utf-8") as f:
            json.dump(project.dict(), f, indent=2, ensure_ascii=False)
        
        return {"project_id": project_id, "message": "Project created successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/load/{project_id}")
async def load_project(project_id: str):
    """Load a project"""
    try:
        project_path = os.path.join("projects", f"{project_id}.json")
        if not os.path.exists(project_path):
            raise HTTPException(status_code=404, detail="Project not found")
        
        with open(project_path, "r", encoding="utf-8") as f:
            project_data = json.load(f)
        
        return project_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/save/{project_id}")
async def save_project(project_id: str, project_data: ProjectData):
    """Save project changes"""
    try:
        project = Project(
            id=project_id,
            name=project_data.name,
            timeline=project_data.timeline,
            tracks=project_data.tracks,
            settings=project_data.settings
        )
        
        project_path = os.path.join("projects", f"{project_id}.json")
        with open(project_path, "w", encoding="utf-8") as f:
            json.dump(project.dict(), f, indent=2, ensure_ascii=False)
        
        return {"message": "Project saved successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/list")
async def list_projects():
    """List all projects"""
    try:
        projects = []
        projects_dir = "projects"
        if os.path.exists(projects_dir):
            for filename in os.listdir(projects_dir):
                if filename.endswith('.json'):
                    project_path = os.path.join(projects_dir, filename)
                    try:
                        with open(project_path, "r", encoding="utf-8") as f:
                            project_data = json.load(f)
                        projects.append({
                            "id": project_data.get("id"),
                            "name": project_data.get("name"),
                            "created_at": project_data.get("created_at"),
                            "updated_at": project_data.get("updated_at")
                        })
                    except:
                        continue
        
        return {"projects": projects}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/delete/{project_id}")
async def delete_project(project_id: str):
    """Delete a project"""
    try:
        project_path = os.path.join("projects", f"{project_id}.json")
        if not os.path.exists(project_path):
            raise HTTPException(status_code=404, detail="Project not found")
        
        os.remove(project_path)
        return {"message": "Project deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
