<template>
  <div class="track-container">
    <div class="track-header">
      <div class="track-info">
        <span class="track-name">{{ track.name }}</span>
        <NButton size="tiny" @click="$emit('remove-track', track.id)">删除</NButton>
      </div>
    </div>
    
    <div
      class="track-content"
      :class="{ 'drag-over': isDragOver }"
      :style="{ width: trackWidth + 'px' }"
      @drop="onDrop"
      @dragover="onDragOver"
      @dragenter="onDragEnter"
      @dragleave="onDragLeave"
    >
      <VideoSegment
        v-for="segment in track.segments"
        :key="segment.id"
        :segment="segment"
        :zoom="zoom"
        @select="$emit('segment-select', segment)"
        @update="$emit('segment-update', segment.id, $event)"
      />
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { NButton } from 'naive-ui'
import VideoSegment from './VideoSegment.vue'
import { useTimelineStore } from '../stores/timeline'

const props = defineProps({
  track: Object,
  zoom: Number,
  scrollLeft: Number
})

const emit = defineEmits(['segment-select', 'segment-update', 'remove-track'])
const timelineStore = useTimelineStore()
const isDragOver = ref(false)

const trackWidth = computed(() => {
  // Calculate track width based on duration and zoom
  const segmentDurations = props.track.segments.length > 0
    ? props.track.segments.map(s => s.endLoc || 0)
    : [0]
  const maxDuration = Math.max(60, ...segmentDurations)
  return maxDuration * 50 * props.zoom
})

// Drag and drop handlers
const onDragOver = (event) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'copy'
}

const onDragEnter = (event) => {
  event.preventDefault()
  isDragOver.value = true
}

const onDragLeave = (event) => {
  event.preventDefault()
  // Only set to false if leaving the track area completely
  if (!event.currentTarget.contains(event.relatedTarget)) {
    isDragOver.value = false
  }
}

const onDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false

  try {
    const data = JSON.parse(event.dataTransfer.getData('application/json'))
    if (data.type === 'video' && data.video) {
      // Calculate drop position in timeline
      const rect = event.currentTarget.getBoundingClientRect()
      const x = event.clientX - rect.left + props.scrollLeft
      const dropTime = x / (50 * props.zoom)

      // Create new segment
      const segment = {
        sourceVideoId: data.video.id,
        startTime: 0,
        endTime: data.video.duration || 10,
        startLoc: Math.max(0, dropTime),
        endLoc: Math.max(0, dropTime + (data.video.duration || 10))
      }

      timelineStore.addSegmentToTrack(props.track.id, segment)
    }
  } catch (error) {
    console.error('Error handling drop:', error)
  }
}
</script>

<style scoped>
.track-container {
  border-bottom: 1px solid #e0e0e0;
  min-height: 60px;
}

.track-header {
  height: 30px;
  background: #f8f8f8;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  padding: 0 12px;
}

.track-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.track-name {
  font-size: 12px;
  font-weight: 500;
}

.track-content {
  height: 60px;
  position: relative;
  background: #fafafa;
  transition: background-color 0.2s;
}

.track-content.drag-over {
  background: #e6f7ff;
  border: 2px dashed #1890ff;
}
</style>
