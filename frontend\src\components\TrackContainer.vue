<template>
  <div class="track-container">
    <div class="track-header">
      <div class="track-info">
        <span class="track-name">{{ track.name }}</span>
        <NButton size="tiny" @click="$emit('remove-track', track.id)">删除</NButton>
      </div>
    </div>
    
    <div class="track-content" :style="{ width: trackWidth + 'px' }">
      <VideoSegment
        v-for="segment in track.segments"
        :key="segment.id"
        :segment="segment"
        :zoom="zoom"
        @select="$emit('segment-select', segment)"
        @update="$emit('segment-update', segment.id, $event)"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { NButton } from 'naive-ui'
import VideoSegment from './VideoSegment.vue'

const props = defineProps({
  track: Object,
  zoom: Number,
  scrollLeft: Number
})

const emit = defineEmits(['segment-select', 'segment-update', 'remove-track'])

const trackWidth = computed(() => {
  // Calculate track width based on duration and zoom
  const maxDuration = Math.max(60, ...props.track.segments.map(s => s.endLoc))
  return maxDuration * 50 * props.zoom
})
</script>

<style scoped>
.track-container {
  border-bottom: 1px solid #e0e0e0;
  min-height: 60px;
}

.track-header {
  height: 30px;
  background: #f8f8f8;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  padding: 0 12px;
}

.track-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.track-name {
  font-size: 12px;
  font-weight: 500;
}

.track-content {
  height: 60px;
  position: relative;
  background: #fafafa;
}
</style>
