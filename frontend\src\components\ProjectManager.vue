<template>
  <NModal v-model:show="show" preset="dialog" title="项目管理">
    <div class="project-manager">
      <div class="project-actions">
        <NSpace>
          <NButton @click="createNewProject" type="primary">新建项目</NButton>
          <NButton @click="saveCurrentProject" :disabled="!hasCurrentProject">保存当前项目</NButton>
        </NSpace>
      </div>
      
      <div class="project-list">
        <h4>项目列表</h4>
        
        <div v-if="loading" class="loading">
          <NSpin size="small" />
          <span>加载中...</span>
        </div>
        
        <div v-else-if="projects.length === 0" class="empty">
          <NEmpty description="暂无项目" />
        </div>
        
        <div v-else class="projects">
          <div 
            v-for="project in projects"
            :key="project.id"
            class="project-item"
          >
            <div class="project-info">
              <div class="project-name">{{ project.name }}</div>
              <div class="project-date">
                创建时间: {{ formatDate(project.created_at) }}
              </div>
              <div class="project-date" v-if="project.updated_at !== project.created_at">
                更新时间: {{ formatDate(project.updated_at) }}
              </div>
            </div>
            
            <div class="project-actions">
              <NSpace>
                <NButton size="small" @click="loadProject(project.id)">加载</NButton>
                <NButton size="small" type="error" @click="deleteProject(project.id)">删除</NButton>
              </NSpace>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #action>
      <NButton @click="show = false">关闭</NButton>
    </template>
  </NModal>
  
  <!-- New Project Dialog -->
  <NModal v-model:show="showNewProjectDialog" preset="dialog" title="新建项目">
    <div class="new-project-form">
      <NFormItem label="项目名称">
        <NInput v-model:value="newProjectName" placeholder="请输入项目名称" />
      </NFormItem>
    </div>
    
    <template #action>
      <NSpace>
        <NButton @click="showNewProjectDialog = false">取消</NButton>
        <NButton type="primary" @click="confirmCreateProject" :disabled="!newProjectName.trim()">
          创建
        </NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { NModal, NButton, NSpace, NSpin, NEmpty, NInput, NFormItem } from 'naive-ui'
import { useTimelineStore } from '../stores/timeline'
import { useProjectStore } from '../stores/project'

const props = defineProps({
  show: Boolean
})

const emit = defineEmits(['update:show'])

const timelineStore = useTimelineStore()
const projectStore = useProjectStore()

const show = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const loading = ref(false)
const projects = ref([])
const showNewProjectDialog = ref(false)
const newProjectName = ref('')

const hasCurrentProject = computed(() => {
  return timelineStore.tracks.length > 0 || timelineStore.duration > 0
})

// Load projects when modal opens
watch(show, async (isVisible) => {
  if (isVisible) {
    await loadProjects()
  }
})

const loadProjects = async () => {
  try {
    loading.value = true
    projects.value = await projectStore.getProjectList()
  } catch (error) {
    console.error('Failed to load projects:', error)
  } finally {
    loading.value = false
  }
}

const createNewProject = () => {
  newProjectName.value = ''
  showNewProjectDialog.value = true
}

const confirmCreateProject = async () => {
  if (!newProjectName.value.trim()) return
  
  try {
    // Clear current timeline
    timelineStore.clearTimeline()
    
    // Create new project
    const projectData = {
      name: newProjectName.value.trim(),
      timeline: timelineStore.exportTimelineData(),
      tracks: [],
      settings: {}
    }
    
    await projectStore.createProject(projectData)
    
    showNewProjectDialog.value = false
    await loadProjects()
  } catch (error) {
    console.error('Failed to create project:', error)
  }
}

const saveCurrentProject = async () => {
  if (!hasCurrentProject.value) return
  
  try {
    const timelineData = timelineStore.exportTimelineData()
    const projectData = {
      name: `项目_${new Date().toLocaleString()}`,
      timeline: timelineData,
      tracks: timelineData.tracks,
      settings: {}
    }
    
    await projectStore.createProject(projectData)
    await loadProjects()
  } catch (error) {
    console.error('Failed to save project:', error)
  }
}

const loadProject = async (projectId) => {
  try {
    const projectData = await projectStore.loadProject(projectId)
    
    // Import timeline data
    timelineStore.importTimelineData({
      tracks: projectData.tracks,
      duration: projectData.timeline.duration,
      currentTime: projectData.timeline.currentTime,
      zoom: projectData.timeline.zoom
    })
    
    show.value = false
  } catch (error) {
    console.error('Failed to load project:', error)
  }
}

const deleteProject = async (projectId) => {
  try {
    await projectStore.deleteProject(projectId)
    await loadProjects()
  } catch (error) {
    console.error('Failed to delete project:', error)
  }
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString()
}
</script>

<style scoped>
.project-manager {
  min-height: 400px;
}

.project-actions {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.project-list h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px;
  color: #666;
}

.empty {
  padding: 40px;
}

.projects {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  transition: all 0.2s;
}

.project-item:hover {
  border-color: #18a058;
  background: #f0f9ff;
}

.project-info {
  flex: 1;
}

.project-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.project-date {
  font-size: 12px;
  color: #666;
}

.new-project-form {
  padding: 16px 0;
}
</style>
