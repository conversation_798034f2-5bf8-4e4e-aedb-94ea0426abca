<template>
  <div 
    class="video-segment"
    :class="{ selected: isSelected }"
    :style="segmentStyle"
    @click="$emit('select')"
    @mousedown="startDrag"
  >
    <div class="segment-content">
      <div class="segment-name">{{ segment.sourceVideoId }}</div>
      <div class="segment-time">
        {{ formatTime(segment.startTime) }} - {{ formatTime(segment.endTime) }}
      </div>
    </div>
    
    <!-- Resize handles -->
    <div class="resize-handle left" @mousedown.stop="startResize('left')"></div>
    <div class="resize-handle right" @mousedown.stop="startResize('right')"></div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  segment: Object,
  zoom: Number,
  isSelected: Boolean
})

const emit = defineEmits(['select', 'update'])

const isDragging = ref(false)
const isResizing = ref(false)
const resizeMode = ref('')

const segmentStyle = computed(() => {
  const pixelsPerSecond = 50 * props.zoom
  const left = props.segment.startLoc * pixelsPerSecond
  const width = (props.segment.endLoc - props.segment.startLoc) * pixelsPerSecond
  
  return {
    left: left + 'px',
    width: width + 'px'
  }
})

const formatTime = (time) => {
  const minutes = Math.floor(time / 60)
  const seconds = (time % 60).toFixed(1)
  return `${minutes}:${seconds.padStart(4, '0')}`
}

const startDrag = (event) => {
  if (isResizing.value) return
  
  isDragging.value = true
  const startX = event.clientX
  const startLoc = props.segment.startLoc
  
  const onMouseMove = (e) => {
    if (!isDragging.value) return
    
    const deltaX = e.clientX - startX
    const deltaTime = deltaX / (50 * props.zoom)
    const newStartLoc = Math.max(0, startLoc + deltaTime)
    const duration = props.segment.endLoc - props.segment.startLoc
    
    emit('update', {
      startLoc: newStartLoc,
      endLoc: newStartLoc + duration
    })
  }
  
  const onMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }
  
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

const startResize = (mode) => {
  isResizing.value = true
  resizeMode.value = mode
  
  const startX = event.clientX
  const startLoc = props.segment.startLoc
  const endLoc = props.segment.endLoc
  
  const onMouseMove = (e) => {
    if (!isResizing.value) return
    
    const deltaX = e.clientX - startX
    const deltaTime = deltaX / (50 * props.zoom)
    
    if (mode === 'left') {
      const newStartLoc = Math.max(0, Math.min(endLoc - 0.1, startLoc + deltaTime))
      emit('update', { startLoc: newStartLoc })
    } else if (mode === 'right') {
      const newEndLoc = Math.max(startLoc + 0.1, endLoc + deltaTime)
      emit('update', { endLoc: newEndLoc })
    }
  }
  
  const onMouseUp = () => {
    isResizing.value = false
    resizeMode.value = ''
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }
  
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}
</script>

<style scoped>
.video-segment {
  position: absolute;
  top: 5px;
  height: 50px;
  background: #3498db;
  border: 1px solid #2980b9;
  border-radius: 4px;
  cursor: move;
  user-select: none;
  overflow: hidden;
}

.video-segment.selected {
  border-color: #e74c3c;
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.3);
}

.segment-content {
  padding: 4px 8px;
  color: white;
  font-size: 11px;
}

.segment-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.segment-time {
  opacity: 0.8;
  margin-top: 2px;
}

.resize-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  background: rgba(255, 255, 255, 0.3);
  cursor: ew-resize;
  opacity: 0;
  transition: opacity 0.2s;
}

.resize-handle.left {
  left: 0;
}

.resize-handle.right {
  right: 0;
}

.video-segment:hover .resize-handle {
  opacity: 1;
}
</style>
